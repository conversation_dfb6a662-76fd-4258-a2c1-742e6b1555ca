#!/bin/bash

# Quick Start Coverage Automation
# Easy-to-use wrapper for your proven manual process

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}============================================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}============================================================${NC}"
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTION] [TARGET]

Automated Code Coverage Collection for Itasca

OPTIONS:
    local-gcc       Run local GCC/gcov coverage (your proven process)
    local-clang     Run local Clang/LLVM coverage  
    local-both      Run both GCC and Clang coverage with comparison
    devtest-gcc     Deploy and run GCC coverage on devtest
    devtest-clang   Deploy and run Clang coverage on devtest
    devtest-both    Deploy and run both coverage types on devtest
    jenkins         Trigger Jenkins coverage pipeline
    help            Show this help message

TARGET (for devtest options):
    Devtest environment hostname/IP (e.g., devtest-01.example.com)

EXAMPLES:
    $0 local-gcc                    # Run local GCC coverage
    $0 local-both                   # Compare GCC vs Clang locally
    $0 devtest-gcc devtest-01       # Run GCC coverage on devtest-01
    $0 jenkins                      # Trigger Jenkins pipeline

ENVIRONMENT VARIABLES:
    SOURCE_DIR      Source directory (default: current directory)
    DEVTEST_TARGET  Default devtest target
    JENKINS_URL     Jenkins server URL
    JENKINS_TOKEN   Jenkins API token

EOF
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check for required tools
    for tool in cmake lcov genhtml; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    # Check for GCC
    if ! command -v gcc >/dev/null 2>&1; then
        missing_tools+=("gcc")
    fi
    
    # Check for Clang (optional)
    if ! command -v clang >/dev/null 2>&1; then
        print_warning "Clang not found - Clang coverage will not be available"
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install missing tools and try again"
        exit 1
    fi
    
    print_status "Prerequisites check passed ✅"
}

# Run local GCC coverage (your proven process)
run_local_gcc_coverage() {
    print_header "Running Local GCC Coverage (Proven Process)"
    
    local workspace="/tmp/gcc_coverage_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$workspace"
    cd "$workspace"
    
    print_status "Workspace: $workspace"
    
    # Step 1: Build instrumented binary
    print_status "Building instrumented binary with coverage flags..."
    cmake --preset x64-linux-cov -S "${SOURCE_DIR:-$(pwd)}"
    cmake --build out/x64-linux-cov
    
    # Verify coverage instrumentation
    local gcno_count=$(find out/x64-linux-cov -name "*.gcno" | wc -l)
    print_status "Found $gcno_count .gcno files ✅"
    
    # Save build directory
    tar -czf build_with_gcno.tar.gz out/x64-linux-cov/
    
    # Step 2: Run unit tests with coverage
    print_status "Running unit tests with coverage collection..."
    cd out/x64-linux-cov
    ctest --output-on-failure
    
    # Step 3: Generate coverage reports
    print_status "Generating coverage reports..."
    
    # Raw coverage data
    lcov --capture --directory . --output-file gtest_coverage_raw.info
    
    # Clean coverage data (your proven filters)
    lcov --remove gtest_coverage_raw.info \
        '/usr/*' \
        '/opt/rh/gcc-toolset-*' \
        '*/vcpkg_installed/*' \
        '*/autogen/krb5*' \
        '*/autogen/rfc-4511*' \
        --output-file gtest_coverage_clean.info
    
    # Generate HTML report
    genhtml gtest_coverage_clean.info --output-directory gtest_coverage_html
    
    # Generate summary
    lcov --summary gtest_coverage_clean.info | tee coverage_summary.txt
    
    print_status "Coverage reports generated successfully ✅"
    print_status "HTML Report: file://$workspace/out/x64-linux-cov/gtest_coverage_html/index.html"
    
    # Show summary
    print_header "Coverage Summary"
    grep -E "(lines|functions)" coverage_summary.txt || echo "Summary not available"
}

# Run local Clang coverage
run_local_clang_coverage() {
    print_header "Running Local Clang Coverage"
    
    if ! command -v clang >/dev/null 2>&1; then
        print_error "Clang not available - cannot run Clang coverage"
        exit 1
    fi
    
    export SOURCE_DIR="${SOURCE_DIR:-$(pwd)}"
    bash "$SCRIPT_DIR/scripts/clang_coverage_workflow.sh" gtest
}

# Run both coverage types with comparison
run_local_both_coverage() {
    print_header "Running Both GCC and Clang Coverage with Comparison"
    
    export SOURCE_DIR="${SOURCE_DIR:-$(pwd)}"
    bash "$SCRIPT_DIR/scripts/unified_coverage_orchestrator.sh" gtest both
}

# Deploy and run devtest coverage
run_devtest_coverage() {
    local coverage_type="$1"
    local target="$2"
    
    if [ -z "$target" ]; then
        print_error "Devtest target required for devtest coverage"
        print_error "Usage: $0 devtest-$coverage_type <target>"
        exit 1
    fi
    
    print_header "Running DevTest Coverage: $coverage_type on $target"
    
    # Check connectivity
    if ! ssh -o ConnectTimeout=5 "$target" "echo 'Connection test'" >/dev/null 2>&1; then
        print_error "Cannot connect to devtest target: $target"
        exit 1
    fi
    
    export DEVTEST_TARGET="$target"
    export SOURCE_DIR="${SOURCE_DIR:-$(pwd)}"
    
    case "$coverage_type" in
        "gcc")
            # Use your proven manual process for devtest
            run_devtest_gcc_coverage "$target"
            ;;
        "clang")
            bash "$SCRIPT_DIR/scripts/clang_coverage_workflow.sh" devtest
            ;;
        "both")
            bash "$SCRIPT_DIR/scripts/unified_coverage_orchestrator.sh" devtest both
            ;;
    esac
}

# Run devtest GCC coverage (your proven process)
run_devtest_gcc_coverage() {
    local target="$1"
    
    print_status "Building instrumented binary..."
    local workspace="/tmp/devtest_gcc_coverage_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$workspace"
    cd "$workspace"
    
    # Build instrumented binary
    cmake --preset x64-linux-cov -S "${SOURCE_DIR}"
    cmake --build out/x64-linux-cov
    
    # Save build artifacts
    tar -czf build_with_gcno.tar.gz out/x64-linux-cov/
    
    print_status "Deploying instrumented binary to $target..."
    
    # Deploy binary
    scp out/x64-linux-cov/src/zpn_brokerd/zpn_brokerd "$target:~/"
    scp "$SCRIPT_DIR/scripts/deploy_instrumented_binary.sh" "$target:~/"
    
    # Execute deployment
    ssh "$target" './deploy_instrumented_binary.sh zpn_brokerd'
    
    print_status "Running devtest suite on $target..."
    
    # Configure coverage environment
    ssh "$target" "
        sudo mkdir -p /tmp/cov
        sudo chown zscaler:zscaler /tmp/cov
        echo 'GCOV_PREFIX=/tmp/cov' | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf
        echo 'GCOV_PREFIX_STRIP=1' | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf
        sudo systemctl restart zpn_brokerd
    "
    
    # Run tests (customize based on your test framework)
    ssh "$target" "
        # Add your actual devtest execution here
        echo 'Running devtest workload...'
        sleep 30  # Placeholder for actual tests
        
        # Graceful shutdown
        itascurl /system/ungraceful_shutdown || sudo systemctl stop zpn_brokerd
    "
    
    print_status "Collecting coverage data from $target..."
    
    # Collect coverage data
    ssh "$target" 'tar -czf devtest_coverage_data.tar.gz /tmp/cov/'
    scp "$target:~/devtest_coverage_data.tar.gz" .
    
    # Process coverage data
    tar -xzf devtest_coverage_data.tar.gz
    cp -r tmp/cov/out/x64-linux-cov/src/* out/x64-linux-cov/src/
    
    cd out/x64-linux-cov
    
    # Generate devtest coverage
    lcov --capture --directory . --output-file devtest_coverage_raw.info
    lcov --remove devtest_coverage_raw.info \
        '/usr/*' '/opt/rh/gcc-toolset-*' '*/vcpkg_installed/*' \
        '*/autogen/krb5*' '*/autogen/rfc-4511*' \
        --output-file devtest_coverage_clean.info
    
    genhtml devtest_coverage_clean.info --output-directory devtest_coverage_html
    lcov --summary devtest_coverage_clean.info | tee devtest_coverage_summary.txt
    
    print_status "DevTest coverage completed ✅"
    print_status "HTML Report: file://$workspace/out/x64-linux-cov/devtest_coverage_html/index.html"
    
    # Show summary
    print_header "DevTest Coverage Summary"
    grep -E "(lines|functions)" devtest_coverage_summary.txt || echo "Summary not available"
}

# Trigger Jenkins pipeline
trigger_jenkins_pipeline() {
    print_header "Triggering Jenkins Coverage Pipeline"
    
    local jenkins_url="${JENKINS_URL:-}"
    local jenkins_token="${JENKINS_TOKEN:-}"
    
    if [ -z "$jenkins_url" ] || [ -z "$jenkins_token" ]; then
        print_error "Jenkins URL and token required"
        print_error "Set JENKINS_URL and JENKINS_TOKEN environment variables"
        exit 1
    fi
    
    local commit_sha=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    
    print_status "Triggering pipeline for commit: $commit_sha"
    
    # Trigger Jenkins job (customize job name and parameters)
    curl -X POST \
        -u "user:$jenkins_token" \
        "$jenkins_url/job/itasca-coverage/buildWithParameters" \
        -d "COMMIT_SHA=$commit_sha" \
        -d "COVERAGE_TYPE=combined"
    
    print_status "Jenkins pipeline triggered ✅"
    print_status "Check progress at: $jenkins_url/job/itasca-coverage/"
}

# Main function
main() {
    local action="${1:-help}"
    local target="${2:-}"
    
    case "$action" in
        "local-gcc")
            check_prerequisites
            run_local_gcc_coverage
            ;;
        "local-clang")
            check_prerequisites
            run_local_clang_coverage
            ;;
        "local-both")
            check_prerequisites
            run_local_both_coverage
            ;;
        "devtest-gcc")
            check_prerequisites
            run_devtest_coverage "gcc" "$target"
            ;;
        "devtest-clang")
            check_prerequisites
            run_devtest_coverage "clang" "$target"
            ;;
        "devtest-both")
            check_prerequisites
            run_devtest_coverage "both" "$target"
            ;;
        "jenkins")
            trigger_jenkins_pipeline
            ;;
        "help"|*)
            show_usage
            ;;
    esac
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
