pipeline {
    agent { label 'generic-large-x86' }

    parameters {
        string(name: 'COMMIT_SHA', defaultValue: '', description: 'Git commit SHA for coverage analysis')
        choice(name: 'COVERAGE_TYPE', choices: ['gtest', 'devtest', 'combined'], description: 'Type of coverage to collect')
        string(name: 'DEVTEST_TARGET', defaultValue: '', description: 'Devtest environment target')
    }

    environment {
        BUILD_AGENT_TAG = "1.29.0"
        JENKINS_BUILD = 1
        COVERAGE_WORKSPACE = "/tmp/coverage-${BUILD_NUMBER}"
        GCOV_PREFIX = "${COVERAGE_WORKSPACE}/gcov-data"
        GCOV_PREFIX_STRIP = "1"
    }

    stages {
        stage('Setup Coverage Environment') {
            steps {
                script {
                    // Create coverage workspace
                    sh """
                        mkdir -p ${COVERAGE_WORKSPACE}/{build,deploy,reports,gcov-data}
                        echo "Coverage workspace: ${COVERAGE_WORKSPACE}"
                    """
                }
            }
        }

        stage('Build Instrumented Binary') {
            agent {
                kubernetes {
                    inheritFrom 'jenkins-agent-itasca-x86'
                    yaml genCoverageBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                }
            }
            steps {
                script {
                    container('itasca') {
                        gitCheckout()

                        // Build with coverage instrumentation
                        sh """
                            cd /tmpfs
                            time scl enable gcc-toolset-14 -- cmake --preset x64-linux-cov
                            time cmake --build out/x64-linux-cov

                            # Verify coverage instrumentation
                            find out/x64-linux-cov -name "*.gcno" | head -5

                            # Save build directory with gcno files
                            tar -czf build_with_gcno.tar.gz out/x64-linux-cov/
                        """

                        // Stash build artifacts
                        stash(name: "coverage-build", includes: "build_with_gcno.tar.gz,out/x64-linux-cov/src/*/zpn_*")
                    }
                }
            }
        }

        stage('Run GTest Coverage') {
            when {
                anyOf {
                    expression { params.COVERAGE_TYPE == 'gtest' }
                    expression { params.COVERAGE_TYPE == 'combined' }
                }
            }
            agent {
                kubernetes {
                    inheritFrom 'jenkins-agent-itasca-x86'
                    yaml genCoverageBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                }
            }
            steps {
                script {
                    container('itasca') {
                        unstash(name: "coverage-build")

                        sh """
                            tar -xzf build_with_gcno.tar.gz
                            cd out/x64-linux-cov

                            # Run unit tests with coverage
                            ctest

                            # Generate gtest coverage
                            lcov --capture --directory . --output-file gtest_coverage_raw.info

                            # Clean coverage data (your proven filters)
                            lcov --remove gtest_coverage_raw.info \\
                                '/usr/*' \\
                                '/opt/rh/gcc-toolset-*' \\
                                '*/vcpkg_installed/*' \\
                                '*/autogen/krb5*' \\
                                '*/autogen/rfc-4511*' \\
                                --output-file gtest_coverage_clean.info

                            # Generate HTML report
                            genhtml gtest_coverage_clean.info --output-directory gtest_coverage_html

                            # Generate summary
                            lcov --summary gtest_coverage_clean.info > gtest_coverage_summary.txt
                        """

                        // Archive gtest results
                        stash(name: "gtest-coverage", includes: "out/x64-linux-cov/gtest_coverage_*")
                        archiveArtifacts artifacts: 'out/x64-linux-cov/gtest_coverage_html/**/*', allowEmptyArchive: true
                    }
                }
            }
        }

        stage('Deploy and Run DevTest Coverage') {
            when {
                anyOf {
                    expression { params.COVERAGE_TYPE == 'devtest' }
                    expression { params.COVERAGE_TYPE == 'combined' }
                }
            }
            steps {
                script {
                    unstash(name: "coverage-build")

                    // Deploy instrumented binary to devtest
                    deployInstrumentedBinary(params.DEVTEST_TARGET)

                    // Run devtest with coverage collection
                    runDevTestWithCoverage(params.DEVTEST_TARGET)

                    // Collect coverage data
                    collectDevTestCoverage(params.DEVTEST_TARGET)
                }
            }
        }

        stage('Generate Combined Coverage Report') {
            when {
                expression { params.COVERAGE_TYPE == 'combined' }
            }
            steps {
                script {
                    unstash(name: "gtest-coverage")
                    unstash(name: "devtest-coverage")

                    sh """
                        cd out/x64-linux-cov

                        # Combine coverage data
                        lcov --add-tracefile gtest_coverage_clean.info \\
                             --add-tracefile devtest_coverage_clean.info \\
                             --output-file combined_coverage.info

                        # Generate combined HTML report
                        genhtml combined_coverage.info --output-directory combined_coverage_html

                        # Generate comprehensive analysis
                        ${WORKSPACE}/automation/scripts/coverage_analysis.sh
                    """

                    archiveArtifacts artifacts: 'out/x64-linux-cov/combined_coverage_html/**/*'
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'out/x64-linux-cov/combined_coverage_html',
                        reportFiles: 'index.html',
                        reportName: 'Combined Coverage Report'
                    ])
                }
            }
        }

        stage('Run Unified Coverage Analysis') {
            when {
                expression { params.COVERAGE_TYPE == 'combined' }
            }
            steps {
                script {
                    // Run unified orchestrator for both GCC and Clang
                    sh """
                        export SOURCE_DIR=${WORKSPACE}
                        export GIT_COMMIT=${env.GIT_COMMIT}
                        ${WORKSPACE}/automation/scripts/unified_coverage_orchestrator.sh combined both
                    """

                    // Archive unified results
                    archiveArtifacts artifacts: '/tmp/unified_coverage_*/reports/**/*', allowEmptyArchive: true

                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '/tmp/unified_coverage_*/reports',
                        reportFiles: 'unified_coverage_report.html',
                        reportName: 'Unified Coverage Report'
                    ])
                }
            }
        }

        stage('Upload Coverage Data') {
            steps {
                script {
                    // Upload to S3 for processing service
                    uploadCoverageToS3(env.GIT_COMMIT, params.COVERAGE_TYPE)
                }
            }
        }
    }

    post {
        always {
            // Cleanup coverage workspace
            sh "rm -rf ${COVERAGE_WORKSPACE}"
        }
        success {
            // Notify success with coverage metrics
            script {
                def coverageMetrics = readFile('out/x64-linux-cov/coverage_summary.txt')
                slackSend(
                    channel: '#datapath-coverage',
                    color: 'good',
                    message: "✅ Coverage analysis completed for ${env.GIT_COMMIT}\n```${coverageMetrics}```"
                )
            }
        }
    }
}

// Helper functions
def deployInstrumentedBinary(target) {
    sh """
        # Create deployment package
        tar -czf instrumented_binary.tar.gz out/x64-linux-cov/src/*/zpn_*

        # Deploy to devtest environment
        scp instrumented_binary.tar.gz ${target}:~/
        scp automation/scripts/deploy_instrumented_binary.sh ${target}:~/

        # Execute deployment
        ssh ${target} './deploy_instrumented_binary.sh'
    """
}

def runDevTestWithCoverage(target) {
    sh """
        # Configure coverage environment on devtest
        ssh ${target} 'sudo mkdir -p /tmp/cov && sudo chown zscaler:zscaler /tmp/cov'

        # Set coverage environment variables
        ssh ${target} 'echo "GCOV_PREFIX=/tmp/cov" | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf'
        ssh ${target} 'echo "GCOV_PREFIX_STRIP=1" | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf'

        # Restart service with coverage
        ssh ${target} 'sudo systemctl restart zpn_brokerd'

        # Run devtest suite
        ssh ${target} './run_devtest_suite.sh'

        # Graceful shutdown to flush coverage
        ssh ${target} 'itascurl /system/ungraceful_shutdown'
    """
}

def collectDevTestCoverage(target) {
    sh """
        # Collect coverage data from devtest
        ssh ${target} 'tar -czf devtest_coverage_data.tar.gz /tmp/cov/'
        scp ${target}:~/devtest_coverage_data.tar.gz .

        # Process devtest coverage
        tar -xzf devtest_coverage_data.tar.gz
        tar -xzf build_with_gcno.tar.gz

        # Co-locate gcda with gcno files
        cp -r tmp/cov/out/x64-linux-cov/src/* out/x64-linux-cov/src/

        cd out/x64-linux-cov

        # Generate devtest coverage
        lcov --capture --directory . --output-file devtest_coverage_raw.info

        # Clean devtest coverage
        lcov --remove devtest_coverage_raw.info \\
            '/usr/*' \\
            '/opt/rh/gcc-toolset-*' \\
            '*/vcpkg_installed/*' \\
            '*/autogen/krb5*' \\
            '*/autogen/rfc-4511*' \\
            --output-file devtest_coverage_clean.info

        # Generate HTML report
        genhtml devtest_coverage_clean.info --output-directory devtest_coverage_html
    """

    stash(name: "devtest-coverage", includes: "out/x64-linux-cov/devtest_coverage_*")
}
