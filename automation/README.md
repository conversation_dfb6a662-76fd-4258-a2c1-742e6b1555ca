# Automated Code Coverage for Itasca

This automation framework builds upon your proven manual coverage process, providing both GCC/gcov and Clang/LLVM coverage workflows with Jenkins CI/CD integration.

## Quick Start

### 1. Run Your Proven GCC Coverage Process (Automated)
```bash
# Automate your exact manual process
./quick_start_coverage.sh local-gcc

# Deploy and run on devtest
./quick_start_coverage.sh devtest-gcc devtest-01.example.com
```

### 2. Try Modern Clang Coverage
```bash
# Run Clang coverage locally
./quick_start_coverage.sh local-clang

# Compare GCC vs Clang
./quick_start_coverage.sh local-both
```

### 3. Full Jenkins Integration
```bash
# Trigger Jenkins pipeline
export JENKINS_URL="https://your-jenkins.com"
export JENKINS_TOKEN="your-token"
./quick_start_coverage.sh jenkins
```

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GCC/GCOV      │    │   Clang/LLVM     │    │   Unified       │
│   Workflow      │───▶│   Workflow       │───▶│   Orchestrator  │
│   (Proven)      │    │   (Modern)       │    │   (Comparison)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Jenkins       │    │   DevTest        │    │   Processing    │
│   Pipeline      │    │   Integration    │    │   Service       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Implementation Options

### Option 1: Automated GCC/GCOV (Recommended First)
**Pros:**
- ✅ Automates your proven manual process
- ✅ Zero risk - same tools, same results
- ✅ Immediate productivity gain
- ✅ Easy Jenkins integration

**Implementation:**
1. Use existing `x64-linux-cov` CMake preset
2. Automate deployment with `deploy_instrumented_binary.sh`
3. Automate coverage collection and reporting
4. Integrate with Jenkins pipeline

### Option 2: Modern Clang/LLVM
**Pros:**
- ✅ Faster build times (2-3x improvement)
- ✅ Better optimization with coverage
- ✅ Source-based coverage (more accurate)
- ✅ Modern reporting features
- ✅ Better CI/CD integration

**Cons:**
- ❌ Newer toolchain (less battle-tested)
- ❌ Potential compatibility issues
- ❌ Different output format

### Option 3: Hybrid Approach (Best of Both)
**Recommended Strategy:**
- **Production CI/CD:** Use GCC/gcov (proven reliability)
- **Development Analysis:** Use Clang/LLVM (better tooling)
- **Comparison Reports:** Run both for validation

## File Structure

```
automation/
├── README.md                           # This file
├── quick_start_coverage.sh             # Easy-to-use wrapper
├── jenkins/
│   └── coverage-gcc-pipeline.groovy    # Jenkins pipeline
├── scripts/
│   ├── deploy_instrumented_binary.sh   # Automated deployment
│   ├── coverage_analysis.sh            # Your proven analysis
│   ├── clang_coverage_workflow.sh      # Modern Clang workflow
│   └── unified_coverage_orchestrator.sh # Both workflows
└── examples/
    └── coverage_comparison_report.html  # Sample output
```

## Usage Examples

### Local Development
```bash
# Quick coverage check during development
./quick_start_coverage.sh local-gcc

# Compare coverage tools
./quick_start_coverage.sh local-both
```

### DevTest Integration
```bash
# Deploy to devtest and collect coverage
export DEVTEST_TARGET="devtest-01.example.com"
./quick_start_coverage.sh devtest-gcc $DEVTEST_TARGET

# Run comprehensive analysis
./quick_start_coverage.sh devtest-both $DEVTEST_TARGET
```

### Jenkins CI/CD
```groovy
// In your Jenkinsfile
stage('Coverage Analysis') {
    steps {
        script {
            // Trigger coverage pipeline
            build job: 'itasca-coverage', parameters: [
                string(name: 'COMMIT_SHA', value: env.GIT_COMMIT),
                choice(name: 'COVERAGE_TYPE', value: 'combined')
            ]
        }
    }
}
```

## Configuration

### Environment Variables
```bash
# Source code location
export SOURCE_DIR="/path/to/itasca"

# DevTest environment
export DEVTEST_TARGET="devtest-01.example.com"

# Jenkins integration
export JENKINS_URL="https://jenkins.example.com"
export JENKINS_TOKEN="your-api-token"

# Coverage processing service
export COVERAGE_UPLOAD_ENDPOINT="http://coverage-service/api/v1/upload"
```

### CMake Presets
Your existing presets work perfectly:
```json
{
  "name": "x64-linux-cov",
  "inherits": ["cc_cov", "linux"],
  "cacheVariables": {
    "CMAKE_C_FLAGS": "-g -fPIC --coverage -fprofile-update=atomic",
    "CMAKE_CXX_FLAGS": "-g -fPIC --coverage -fprofile-update=atomic"
  }
}
```

## Expected Results

Based on your manual process, expect these results:

### GTest Coverage (Unit Tests)
```
lines......: 21.3% (66,483 of 312,587 lines)
functions..: 43.9% (10,027 of 22,865 functions)
```

### DevTest Coverage (Integration Tests)
```
lines......: 21.0% (60,326 of 287,003 lines)
functions..: 26.8% (4,192 of 15,644 functions)
```

### Combined Coverage
```
lines......: 34.2% (111,818 of 326,552 lines)
functions..: 55.9% (13,091 of 23,419 functions)
```

### Coverage Overlap Analysis
```
Files with gtest coverage: 851
Files with devtest coverage: 655
Files covered by BOTH: 628 (72.2% overlap)
```

## Migration Path

### Phase 1: Automate Current Process (Week 1)
1. Deploy automation scripts
2. Test local GCC coverage automation
3. Validate results match manual process

### Phase 2: Jenkins Integration (Week 2)
1. Deploy Jenkins pipeline
2. Configure S3 upload
3. Test end-to-end automation

### Phase 3: DevTest Automation (Week 3)
1. Automate devtest deployment
2. Integrate with test execution
3. Automate coverage collection

### Phase 4: Modern Tooling (Week 4)
1. Add Clang coverage workflow
2. Implement comparison analysis
3. Deploy unified orchestrator

## Troubleshooting

### Common Issues

**No coverage data generated:**
```bash
# Check instrumentation
find out/x64-linux-cov -name "*.gcno" | wc -l

# Verify environment variables
echo $GCOV_PREFIX
echo $GCOV_PREFIX_STRIP
```

**Service won't start after deployment:**
```bash
# Check binary permissions
ls -la /opt/zscaler/bin/zpn_brokerd

# Check capabilities
getcap /opt/zscaler/bin/zpn_brokerd

# Check SELinux context
ls -Z /opt/zscaler/bin/zpn_brokerd
```

**Coverage data collection fails:**
```bash
# Verify graceful shutdown
itascurl /system/ungraceful_shutdown

# Check coverage directory
ls -la /tmp/cov/

# Verify gcda files
find /tmp/cov -name "*.gcda" | wc -l
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review logs in `/tmp/coverage_*/`
3. Compare with your proven manual process
4. Contact the development team

## Next Steps

1. **Start with GCC automation** - lowest risk, immediate value
2. **Add Jenkins integration** - scale to team usage
3. **Experiment with Clang** - evaluate modern tooling
4. **Implement processing service** - centralized reporting
5. **Add trend analysis** - track coverage over time
