#!/usr/bin/env bash

# Automated deployment script for instrumented binaries
# Based on your proven manual process

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/tmp/coverage_deployment.log"
BACKUP_DIR="/tmp/binary_backup_$(date +%Y%m%d_%H%M%S)"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Backup current binary
backup_current_binary() {
    log "Creating backup of current binary"
    mkdir -p "$BACKUP_DIR"
    
    if [ -f "/opt/zscaler/bin/zpn_brokerd" ]; then
        cp "/opt/zscaler/bin/zpn_brokerd" "$BACKUP_DIR/"
        log "Backup created at $BACKUP_DIR/zpn_brokerd"
    else
        log "No existing binary found to backup"
    fi
}

# Restore binary from backup
restore_binary() {
    if [ -f "$BACKUP_DIR/zpn_brokerd" ]; then
        log "Restoring binary from backup"
        sudo cp "$BACKUP_DIR/zpn_brokerd" "/opt/zscaler/bin/zpn_brokerd"
        sudo /sbin/restorecon -v /opt/zscaler/bin/zpn_brokerd
        sudo chown zscaler:zscaler /opt/zscaler/bin/zpn_brokerd
        sudo setcap CAP_NET_BIND_SERVICE,CAP_SYS_PTRACE,cap_net_raw,cap_net_admin=ep /opt/zscaler/bin/zpn_brokerd
        log "Binary restored successfully"
    else
        error_exit "No backup found to restore"
    fi
}

# Deploy instrumented binary
deploy_instrumented_binary() {
    local binary_path="$1"
    
    log "Deploying instrumented binary: $binary_path"
    
    # Verify binary exists and is executable
    if [ ! -f "$binary_path" ]; then
        error_exit "Binary not found: $binary_path"
    fi
    
    if [ ! -x "$binary_path" ]; then
        error_exit "Binary is not executable: $binary_path"
    fi
    
    # Check if binary is different from current
    if [ -f "/opt/zscaler/bin/zpn_brokerd" ]; then
        if diff "$binary_path" "/opt/zscaler/bin/zpn_brokerd" >/dev/null 2>&1; then
            log "WARNING: Binary is identical to current version"
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                error_exit "Deployment cancelled by user"
            fi
        fi
    fi
    
    # Deploy the binary
    log "Copying binary to /opt/zscaler/bin/"
    sudo cp "$binary_path" "/opt/zscaler/bin/zpn_brokerd"
    
    # Fix SELinux security context
    log "Fixing SELinux context"
    sudo /sbin/restorecon -v /opt/zscaler/bin/zpn_brokerd
    
    # Set proper ownership and capabilities
    log "Setting ownership and capabilities"
    sudo chown zscaler:zscaler /opt/zscaler/bin/zpn_brokerd
    sudo setcap CAP_NET_BIND_SERVICE,CAP_SYS_PTRACE,cap_net_raw,cap_net_admin=ep /opt/zscaler/bin/zpn_brokerd
    
    log "Binary deployment completed successfully"
}

# Configure coverage environment
configure_coverage_environment() {
    log "Configuring coverage environment"
    
    # Create coverage directory
    sudo mkdir -p /tmp/cov
    sudo chown zscaler:zscaler /tmp/cov
    sudo chmod 755 /tmp/cov
    
    # Backup current configuration
    if [ -f "/opt/zscaler/etc/zpn_brokerd.conf" ]; then
        sudo cp "/opt/zscaler/etc/zpn_brokerd.conf" "$BACKUP_DIR/zpn_brokerd.conf.backup"
    fi
    
    # Configure coverage environment variables
    log "Setting coverage environment variables"
    
    # Remove existing coverage variables if present
    sudo sed -i '/^GCOV_PREFIX/d' /opt/zscaler/etc/zpn_brokerd.conf 2>/dev/null || true
    sudo sed -i '/^GCOV_PREFIX_STRIP/d' /opt/zscaler/etc/zpn_brokerd.conf 2>/dev/null || true
    
    # Add coverage environment variables
    echo "GCOV_PREFIX=/tmp/cov" | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf
    echo "GCOV_PREFIX_STRIP=1" | sudo tee -a /opt/zscaler/etc/zpn_brokerd.conf
    
    log "Coverage environment configured"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment"
    
    # Check binary exists and has correct permissions
    if [ ! -f "/opt/zscaler/bin/zpn_brokerd" ]; then
        error_exit "Deployed binary not found"
    fi
    
    # Check ownership
    local owner=$(stat -c '%U:%G' /opt/zscaler/bin/zpn_brokerd)
    if [ "$owner" != "zscaler:zscaler" ]; then
        error_exit "Incorrect ownership: $owner (expected zscaler:zscaler)"
    fi
    
    # Check capabilities
    local caps=$(getcap /opt/zscaler/bin/zpn_brokerd)
    if [[ ! "$caps" =~ "cap_net_bind_service" ]]; then
        error_exit "Missing required capabilities"
    fi
    
    # Check coverage directory
    if [ ! -d "/tmp/cov" ]; then
        error_exit "Coverage directory not created"
    fi
    
    local cov_owner=$(stat -c '%U:%G' /tmp/cov)
    if [ "$cov_owner" != "zscaler:zscaler" ]; then
        error_exit "Incorrect coverage directory ownership: $cov_owner"
    fi
    
    log "Deployment verification successful"
}

# Restart service with coverage
restart_service_with_coverage() {
    log "Restarting service with coverage configuration"
    
    # Stop service gracefully
    if sudo systemctl is-active --quiet zpn_brokerd; then
        log "Stopping zpn_brokerd service"
        sudo systemctl stop zpn_brokerd
        
        # Wait for service to stop
        local timeout=30
        while sudo systemctl is-active --quiet zpn_brokerd && [ $timeout -gt 0 ]; do
            sleep 1
            ((timeout--))
        done
        
        if sudo systemctl is-active --quiet zpn_brokerd; then
            error_exit "Service failed to stop within timeout"
        fi
    fi
    
    # Clear any existing coverage data
    log "Clearing existing coverage data"
    sudo rm -rf /tmp/cov/*
    
    # Start service
    log "Starting zpn_brokerd service"
    sudo systemctl start zpn_brokerd
    
    # Wait for service to start
    local timeout=30
    while ! sudo systemctl is-active --quiet zpn_brokerd && [ $timeout -gt 0 ]; do
        sleep 1
        ((timeout--))
    done
    
    if ! sudo systemctl is-active --quiet zpn_brokerd; then
        error_exit "Service failed to start within timeout"
    fi
    
    log "Service restarted successfully"
    
    # Show service status
    sudo systemctl status zpn_brokerd --no-pager
}

# Main deployment function
main() {
    local binary_path="${1:-}"
    
    if [ -z "$binary_path" ]; then
        # Extract from tar if provided
        if [ -f "instrumented_binary.tar.gz" ]; then
            log "Extracting instrumented binary from archive"
            tar -xzf instrumented_binary.tar.gz
            
            # Find the zpn_brokerd binary
            binary_path=$(find . -name "zpn_brokerd" -type f | head -1)
            
            if [ -z "$binary_path" ]; then
                error_exit "zpn_brokerd binary not found in archive"
            fi
        else
            error_exit "Usage: $0 <binary_path> or provide instrumented_binary.tar.gz"
        fi
    fi
    
    log "Starting instrumented binary deployment"
    log "Binary path: $binary_path"
    
    # Trap to restore on error
    trap 'log "Deployment failed, attempting to restore"; restore_binary' ERR
    
    # Deployment steps
    backup_current_binary
    deploy_instrumented_binary "$binary_path"
    configure_coverage_environment
    verify_deployment
    restart_service_with_coverage
    
    log "Instrumented binary deployment completed successfully"
    log "Coverage data will be collected in /tmp/cov/"
    log "Backup available at: $BACKUP_DIR"
    
    # Remove trap
    trap - ERR
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
