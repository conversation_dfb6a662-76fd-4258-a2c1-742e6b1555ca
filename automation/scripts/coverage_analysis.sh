#!/bin/bash

# Automated coverage analysis script
# Replicates your proven manual analysis process

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ANALYSIS_DIR="coverage_analysis_$(date +%Y%m%d_%H%M%S)"
REPORT_FILE="coverage_comprehensive_report.txt"

# Create analysis workspace
mkdir -p "$ANALYSIS_DIR"
cd "$ANALYSIS_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$REPORT_FILE"
}

# Generate coverage overlap analysis
generate_overlap_analysis() {
    log "=== COVERAGE OVERLAP ANALYSIS ==="
    
    # Extract filenames from coverage reports
    if [ -f "../gtest_coverage_clean.info" ]; then
        lcov --list ../gtest_coverage_clean.info | \
            grep -E "\.(c|cpp)\s*\|" | \
            sed 's/\s*|.*//' | \
            sed 's/^\s*//' | \
            sort -u > gtest_files.txt
    else
        touch gtest_files.txt
        log "WARNING: gtest coverage file not found"
    fi
    
    if [ -f "../devtest_coverage_clean.info" ]; then
        lcov --list ../devtest_coverage_clean.info | \
            grep -E "\.(c|cpp)\s*\|" | \
            sed 's/\s*|.*//' | \
            sed 's/^\s*//' | \
            sort -u > devtest_files.txt
    else
        touch devtest_files.txt
        log "WARNING: devtest coverage file not found"
    fi
    
    # Calculate statistics
    local gtest_files=$(cat gtest_files.txt | wc -l)
    local devtest_files=$(cat devtest_files.txt | wc -l)
    local both_files=$(comm -12 gtest_files.txt devtest_files.txt | wc -l)
    local gtest_only=$(comm -23 gtest_files.txt devtest_files.txt | wc -l)
    local devtest_only=$(comm -13 gtest_files.txt devtest_files.txt | wc -l)
    local total_unique=$(cat gtest_files.txt devtest_files.txt | sort -u | wc -l)
    
    log "Files with gtest coverage:      $gtest_files"
    log "Files with devtest coverage:    $devtest_files"
    log "Files covered by BOTH:          $both_files"
    log "Files covered by gtest ONLY:    $gtest_only"
    log "Files covered by devtest ONLY:  $devtest_only"
    log "Total unique files:             $total_unique"
    
    if [ $total_unique -gt 0 ]; then
        local overlap_pct=$(awk "BEGIN {printf \"%.1f\", $both_files * 100 / $total_unique}")
        local gtest_unique_pct=$(awk "BEGIN {printf \"%.1f\", $gtest_only * 100 / $total_unique}")
        local devtest_unique_pct=$(awk "BEGIN {printf \"%.1f\", $devtest_only * 100 / $total_unique}")
        
        log ""
        log "=== OVERLAP PERCENTAGES ==="
        log "Coverage Overlap:               $overlap_pct%"
        log "gtest Unique Coverage:          $gtest_unique_pct%"
        log "devtest Unique Coverage:        $devtest_unique_pct%"
        
        # Generate overlap details
        log ""
        log "=== FILES COVERED BY BOTH TEST SUITES ==="
        comm -12 gtest_files.txt devtest_files.txt | head -20 | while read file; do
            log "  $file"
        done
        
        if [ $both_files -gt 20 ]; then
            log "  ... and $(($both_files - 20)) more files"
        fi
        
        log ""
        log "=== FILES COVERED ONLY BY DEVTEST ==="
        comm -13 gtest_files.txt devtest_files.txt | head -10 | while read file; do
            log "  $file"
        done
        
        if [ $devtest_only -gt 10 ]; then
            log "  ... and $(($devtest_only - 10)) more files"
        fi
    fi
    
    log ""
}

# Generate coverage summaries
generate_coverage_summaries() {
    log "============================================================"
    log "DATAPATH CODE COVERAGE SUMMARY REPORT"
    log "============================================================"
    log ""
    
    log "=== GTEST COVERAGE (Unit Tests) ==="
    if [ -f "../gtest_coverage_clean.info" ]; then
        lcov --summary ../gtest_coverage_clean.info | grep -E "(lines|functions)" | while read line; do
            log "$line"
        done
        
        # Extract numeric values for metrics
        local gtest_line_pct=$(lcov --summary ../gtest_coverage_clean.info | grep "lines" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        local gtest_func_pct=$(lcov --summary ../gtest_coverage_clean.info | grep "functions" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        
        echo "GTEST_LINE_COVERAGE=$gtest_line_pct" >> ../coverage_metrics.env
        echo "GTEST_FUNCTION_COVERAGE=$gtest_func_pct" >> ../coverage_metrics.env
    else
        log "gtest coverage file not found"
        echo "GTEST_LINE_COVERAGE=0" >> ../coverage_metrics.env
        echo "GTEST_FUNCTION_COVERAGE=0" >> ../coverage_metrics.env
    fi
    log ""
    
    log "=== DEVTEST COVERAGE (Integration Tests) ==="
    if [ -f "../devtest_coverage_clean.info" ]; then
        lcov --summary ../devtest_coverage_clean.info | grep -E "(lines|functions)" | while read line; do
            log "$line"
        done
        
        # Extract numeric values for metrics
        local devtest_line_pct=$(lcov --summary ../devtest_coverage_clean.info | grep "lines" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        local devtest_func_pct=$(lcov --summary ../devtest_coverage_clean.info | grep "functions" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        
        echo "DEVTEST_LINE_COVERAGE=$devtest_line_pct" >> ../coverage_metrics.env
        echo "DEVTEST_FUNCTION_COVERAGE=$devtest_func_pct" >> ../coverage_metrics.env
    else
        log "devtest coverage file not found"
        echo "DEVTEST_LINE_COVERAGE=0" >> ../coverage_metrics.env
        echo "DEVTEST_FUNCTION_COVERAGE=0" >> ../coverage_metrics.env
    fi
    log ""
    
    log "=== COMBINED COVERAGE (gtest + devtest) ==="
    if [ -f "../combined_coverage.info" ]; then
        lcov --summary ../combined_coverage.info | grep -E "(lines|functions)" | while read line; do
            log "$line"
        done
        
        # Extract numeric values for metrics
        local combined_line_pct=$(lcov --summary ../combined_coverage.info | grep "lines" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        local combined_func_pct=$(lcov --summary ../combined_coverage.info | grep "functions" | sed 's/.*: \([0-9.]*\)%.*/\1/')
        
        echo "COMBINED_LINE_COVERAGE=$combined_line_pct" >> ../coverage_metrics.env
        echo "COMBINED_FUNCTION_COVERAGE=$combined_func_pct" >> ../coverage_metrics.env
    else
        log "combined coverage file not found"
        echo "COMBINED_LINE_COVERAGE=0" >> ../coverage_metrics.env
        echo "COMBINED_FUNCTION_COVERAGE=0" >> ../coverage_metrics.env
    fi
    log ""
    
    log "============================================================"
}

# Generate detailed file-level analysis
generate_file_analysis() {
    log "=== DETAILED FILE-LEVEL COVERAGE ANALYSIS ==="
    
    if [ -f "../combined_coverage.info" ]; then
        # Top 10 best covered files
        log ""
        log "=== TOP 10 BEST COVERED FILES ==="
        lcov --list ../combined_coverage.info | \
            grep -E "\.(c|cpp)\s*\|" | \
            sort -k2 -nr | \
            head -10 | \
            while read line; do
                log "$line"
            done
        
        # Top 10 worst covered files (with some coverage)
        log ""
        log "=== TOP 10 WORST COVERED FILES (with some coverage) ==="
        lcov --list ../combined_coverage.info | \
            grep -E "\.(c|cpp)\s*\|" | \
            grep -v "0.0%" | \
            sort -k2 -n | \
            head -10 | \
            while read line; do
                log "$line"
            done
        
        # Files with zero coverage
        log ""
        log "=== FILES WITH ZERO COVERAGE ==="
        local zero_coverage_count=$(lcov --list ../combined_coverage.info | grep -E "\.(c|cpp)\s*\|" | grep "0.0%" | wc -l)
        log "Total files with zero coverage: $zero_coverage_count"
        
        if [ $zero_coverage_count -gt 0 ]; then
            lcov --list ../combined_coverage.info | \
                grep -E "\.(c|cpp)\s*\|" | \
                grep "0.0%" | \
                head -20 | \
                while read line; do
                    log "$line"
                done
            
            if [ $zero_coverage_count -gt 20 ]; then
                log "... and $(($zero_coverage_count - 20)) more files with zero coverage"
            fi
        fi
    fi
    
    log ""
}

# Generate coverage trends (if historical data available)
generate_coverage_trends() {
    log "=== COVERAGE TRENDS ==="
    
    # This would be enhanced with historical data storage
    local current_date=$(date +%Y-%m-%d)
    local git_commit=${GIT_COMMIT:-$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")}
    
    if [ -f "../coverage_metrics.env" ]; then
        source ../coverage_metrics.env
        
        # Create trend entry
        echo "$current_date,$git_commit,$COMBINED_LINE_COVERAGE,$COMBINED_FUNCTION_COVERAGE,$GTEST_LINE_COVERAGE,$DEVTEST_LINE_COVERAGE" >> ../coverage_trends.csv
        
        log "Coverage metrics for commit $git_commit:"
        log "  Combined Line Coverage: $COMBINED_LINE_COVERAGE%"
        log "  Combined Function Coverage: $COMBINED_FUNCTION_COVERAGE%"
        log "  GTest Line Coverage: $GTEST_LINE_COVERAGE%"
        log "  DevTest Line Coverage: $DEVTEST_LINE_COVERAGE%"
    fi
    
    log ""
}

# Generate actionable recommendations
generate_recommendations() {
    log "=== ACTIONABLE RECOMMENDATIONS ==="
    
    if [ -f "../coverage_metrics.env" ]; then
        source ../coverage_metrics.env
        
        # Line coverage recommendations
        if (( $(echo "$COMBINED_LINE_COVERAGE < 40" | bc -l) )); then
            log "🔴 CRITICAL: Combined line coverage is below 40% ($COMBINED_LINE_COVERAGE%)"
            log "   Recommendation: Focus on increasing overall test coverage"
        elif (( $(echo "$COMBINED_LINE_COVERAGE < 60" | bc -l) )); then
            log "🟡 WARNING: Combined line coverage is below 60% ($COMBINED_LINE_COVERAGE%)"
            log "   Recommendation: Add more comprehensive tests"
        else
            log "✅ GOOD: Combined line coverage is above 60% ($COMBINED_LINE_COVERAGE%)"
        fi
        
        # Function coverage recommendations
        if (( $(echo "$COMBINED_FUNCTION_COVERAGE < 50" | bc -l) )); then
            log "🔴 CRITICAL: Combined function coverage is below 50% ($COMBINED_FUNCTION_COVERAGE%)"
            log "   Recommendation: Focus on testing more functions"
        elif (( $(echo "$COMBINED_FUNCTION_COVERAGE < 70" | bc -l) )); then
            log "🟡 WARNING: Combined function coverage is below 70% ($COMBINED_FUNCTION_COVERAGE%)"
            log "   Recommendation: Add tests for uncovered functions"
        else
            log "✅ GOOD: Combined function coverage is above 70% ($COMBINED_FUNCTION_COVERAGE%)"
        fi
        
        # Test suite balance recommendations
        local gtest_devtest_diff=$(echo "$GTEST_LINE_COVERAGE - $DEVTEST_LINE_COVERAGE" | bc -l)
        if (( $(echo "$gtest_devtest_diff > 10" | bc -l) )); then
            log "📊 INSIGHT: GTest coverage significantly higher than DevTest"
            log "   Recommendation: Consider adding more integration tests"
        elif (( $(echo "$gtest_devtest_diff < -10" | bc -l) )); then
            log "📊 INSIGHT: DevTest coverage significantly higher than GTest"
            log "   Recommendation: Consider adding more unit tests"
        else
            log "✅ BALANCED: Good balance between unit and integration test coverage"
        fi
    fi
    
    log ""
}

# Main analysis function
main() {
    log "Starting comprehensive coverage analysis"
    log "Analysis directory: $(pwd)"
    log "Timestamp: $(date)"
    log ""
    
    # Run all analysis components
    generate_coverage_summaries
    generate_overlap_analysis
    generate_file_analysis
    generate_coverage_trends
    generate_recommendations
    
    log "Coverage analysis completed"
    log "Full report available at: $REPORT_FILE"
    
    # Copy report to parent directory
    cp "$REPORT_FILE" "../coverage_analysis_report.txt"
    
    # Generate JSON summary for programmatic consumption
    if [ -f "../coverage_metrics.env" ]; then
        source ../coverage_metrics.env
        cat > ../coverage_summary.json << EOF
{
    "timestamp": "$(date -Iseconds)",
    "git_commit": "${GIT_COMMIT:-unknown}",
    "coverage_metrics": {
        "combined_line_coverage": $COMBINED_LINE_COVERAGE,
        "combined_function_coverage": $COMBINED_FUNCTION_COVERAGE,
        "gtest_line_coverage": $GTEST_LINE_COVERAGE,
        "gtest_function_coverage": $GTEST_FUNCTION_COVERAGE,
        "devtest_line_coverage": $DEVTEST_LINE_COVERAGE,
        "devtest_function_coverage": $DEVTEST_FUNCTION_COVERAGE
    },
    "file_statistics": {
        "gtest_files": $(cat gtest_files.txt | wc -l),
        "devtest_files": $(cat devtest_files.txt | wc -l),
        "overlap_files": $(comm -12 gtest_files.txt devtest_files.txt | wc -l),
        "total_unique_files": $(cat gtest_files.txt devtest_files.txt | sort -u | wc -l)
    }
}
EOF
    fi
}

# Execute main function
main "$@"
