#!/bin/bash

# Unified Coverage Orchestrator
# Runs both GCC/gcov and Clang/LLVM coverage workflows with comparison analysis

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ORCHESTRATOR_WORKSPACE="/tmp/unified_coverage_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$ORCHESTRATOR_WORKSPACE/orchestrator.log"

# Configuration
GCC_WORKFLOW_SCRIPT="$SCRIPT_DIR/gcc_coverage_workflow.sh"
CLANG_WORKFLOW_SCRIPT="$SCRIPT_DIR/clang_coverage_workflow.sh"
ANALYSIS_SCRIPT="$SCRIPT_DIR/coverage_analysis.sh"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ORCHESTRATOR: $*" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Setup orchestrator workspace
setup_orchestrator() {
    log "Setting up unified coverage orchestrator"
    mkdir -p "$ORCHESTRATOR_WORKSPACE"/{gcc,clang,comparison,reports}
    
    log "Orchestrator workspace: $ORCHESTRATOR_WORKSPACE"
}

# Run GCC/gcov workflow
run_gcc_workflow() {
    local workflow_type="$1"
    
    log "Starting GCC/gcov coverage workflow: $workflow_type"
    
    export WORKSPACE_DIR="$ORCHESTRATOR_WORKSPACE/gcc"
    export SOURCE_DIR="${SOURCE_DIR:-$(pwd)}"
    
    if [ ! -f "$GCC_WORKFLOW_SCRIPT" ]; then
        log "WARNING: GCC workflow script not found, using manual process"
        run_manual_gcc_workflow "$workflow_type"
    else
        bash "$GCC_WORKFLOW_SCRIPT" "$workflow_type"
    fi
    
    log "GCC/gcov workflow completed"
}

# Run manual GCC workflow (based on your proven process)
run_manual_gcc_workflow() {
    local workflow_type="$1"
    local gcc_workspace="$ORCHESTRATOR_WORKSPACE/gcc"
    
    log "Running manual GCC coverage workflow"
    
    mkdir -p "$gcc_workspace"/{build,reports}
    cd "$gcc_workspace/build"
    
    # Build with coverage
    log "Building with GCC coverage instrumentation"
    cmake --preset x64-linux-cov -S "${SOURCE_DIR}"
    cmake --build out/x64-linux-cov
    
    # Save build artifacts
    tar -czf build_with_gcno.tar.gz out/x64-linux-cov/
    
    case "$workflow_type" in
        "gtest"|"combined")
            log "Running GTest coverage"
            cd out/x64-linux-cov
            ctest
            
            # Generate gtest coverage
            lcov --capture --directory . --output-file gtest_coverage_raw.info
            lcov --remove gtest_coverage_raw.info \
                '/usr/*' '/opt/rh/gcc-toolset-*' '*/vcpkg_installed/*' \
                '*/autogen/krb5*' '*/autogen/rfc-4511*' \
                --output-file gtest_coverage_clean.info
            
            genhtml gtest_coverage_clean.info --output-directory "$gcc_workspace/reports/gtest_html"
            lcov --summary gtest_coverage_clean.info > "$gcc_workspace/reports/gtest_summary.txt"
            ;;
    esac
    
    case "$workflow_type" in
        "devtest"|"combined")
            log "Running DevTest coverage (placeholder - requires actual devtest environment)"
            # This would require actual devtest deployment
            log "DevTest coverage requires manual deployment to devtest environment"
            ;;
    esac
}

# Run Clang/LLVM workflow
run_clang_workflow() {
    local workflow_type="$1"
    
    log "Starting Clang/LLVM coverage workflow: $workflow_type"
    
    export WORKSPACE_DIR="$ORCHESTRATOR_WORKSPACE/clang"
    export SOURCE_DIR="${SOURCE_DIR:-$(pwd)}"
    
    if [ ! -f "$CLANG_WORKFLOW_SCRIPT" ]; then
        error_exit "Clang workflow script not found: $CLANG_WORKFLOW_SCRIPT"
    fi
    
    bash "$CLANG_WORKFLOW_SCRIPT" "$workflow_type"
    
    log "Clang/LLVM workflow completed"
}

# Compare coverage results
compare_coverage_results() {
    log "Comparing GCC and Clang coverage results"
    
    local comparison_dir="$ORCHESTRATOR_WORKSPACE/comparison"
    local comparison_report="$comparison_dir/coverage_comparison.txt"
    
    mkdir -p "$comparison_dir"
    
    {
        echo "============================================================"
        echo "GCC vs CLANG COVERAGE COMPARISON REPORT"
        echo "============================================================"
        echo "Generated: $(date)"
        echo ""
        
        # GCC Results
        echo "=== GCC/GCOV RESULTS ==="
        if [ -f "$ORCHESTRATOR_WORKSPACE/gcc/reports/gtest_summary.txt" ]; then
            grep -E "(lines|functions)" "$ORCHESTRATOR_WORKSPACE/gcc/reports/gtest_summary.txt" || echo "No GCC summary available"
        else
            echo "GCC results not available"
        fi
        echo ""
        
        # Clang Results
        echo "=== CLANG/LLVM RESULTS ==="
        if [ -f "$ORCHESTRATOR_WORKSPACE/clang/reports/combined/summary.txt" ]; then
            head -10 "$ORCHESTRATOR_WORKSPACE/clang/reports/combined/summary.txt" || echo "No Clang summary available"
        else
            echo "Clang results not available"
        fi
        echo ""
        
        # Performance Comparison
        echo "=== PERFORMANCE COMPARISON ==="
        compare_performance_metrics
        echo ""
        
        # Tool-specific Analysis
        echo "=== TOOL-SPECIFIC ANALYSIS ==="
        analyze_tool_differences
        echo ""
        
        echo "============================================================"
    } > "$comparison_report"
    
    log "Comparison report generated: $comparison_report"
}

# Compare performance metrics
compare_performance_metrics() {
    echo "Build Performance:"
    
    # Extract build times if available
    local gcc_build_time="Unknown"
    local clang_build_time="Unknown"
    
    if [ -f "$ORCHESTRATOR_WORKSPACE/gcc/build.log" ]; then
        gcc_build_time=$(grep "Build time:" "$ORCHESTRATOR_WORKSPACE/gcc/build.log" | tail -1 | cut -d: -f2 || echo "Unknown")
    fi
    
    if [ -f "$ORCHESTRATOR_WORKSPACE/clang/clang_coverage.log" ]; then
        clang_build_time=$(grep "Build completed" "$ORCHESTRATOR_WORKSPACE/clang/clang_coverage.log" | tail -1 || echo "Unknown")
    fi
    
    echo "  GCC Build Time: $gcc_build_time"
    echo "  Clang Build Time: $clang_build_time"
    echo ""
    
    echo "Coverage Data Size:"
    local gcc_data_size="Unknown"
    local clang_data_size="Unknown"
    
    if [ -d "$ORCHESTRATOR_WORKSPACE/gcc" ]; then
        gcc_data_size=$(du -sh "$ORCHESTRATOR_WORKSPACE/gcc" | cut -f1)
    fi
    
    if [ -d "$ORCHESTRATOR_WORKSPACE/clang" ]; then
        clang_data_size=$(du -sh "$ORCHESTRATOR_WORKSPACE/clang" | cut -f1)
    fi
    
    echo "  GCC Data Size: $gcc_data_size"
    echo "  Clang Data Size: $clang_data_size"
}

# Analyze tool-specific differences
analyze_tool_differences() {
    echo "Tool Capabilities:"
    echo "  GCC/GCOV:"
    echo "    ✅ Mature and stable"
    echo "    ✅ Wide compatibility"
    echo "    ✅ Proven in production"
    echo "    ❌ Slower instrumentation"
    echo "    ❌ Limited modern features"
    echo ""
    echo "  Clang/LLVM:"
    echo "    ✅ Faster instrumentation"
    echo "    ✅ Better optimization"
    echo "    ✅ Modern reporting features"
    echo "    ✅ Source-based coverage"
    echo "    ❌ Newer, less battle-tested"
    echo "    ❌ Potential compatibility issues"
    echo ""
    
    echo "Recommendations:"
    echo "  • Use GCC/GCOV for production coverage collection (proven reliability)"
    echo "  • Use Clang/LLVM for development and detailed analysis (better tooling)"
    echo "  • Consider hybrid approach: GCC for CI/CD, Clang for deep analysis"
}

# Generate unified report
generate_unified_report() {
    log "Generating unified coverage report"
    
    local unified_report="$ORCHESTRATOR_WORKSPACE/reports/unified_coverage_report.html"
    
    cat > "$unified_report" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Unified Coverage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .gcc-section { border-left: 5px solid #4CAF50; }
        .clang-section { border-left: 5px solid #2196F3; }
        .comparison-section { border-left: 5px solid #FF9800; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #e8f4f8; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Unified Code Coverage Report</h1>
        <p>Generated: $(date)</p>
        <p>Workspace: $ORCHESTRATOR_WORKSPACE</p>
    </div>
    
    <div class="section gcc-section">
        <h2>GCC/GCOV Results</h2>
        <div id="gcc-results">
EOF
    
    # Add GCC results if available
    if [ -f "$ORCHESTRATOR_WORKSPACE/gcc/reports/gtest_summary.txt" ]; then
        echo "            <pre>" >> "$unified_report"
        cat "$ORCHESTRATOR_WORKSPACE/gcc/reports/gtest_summary.txt" >> "$unified_report"
        echo "            </pre>" >> "$unified_report"
    else
        echo "            <p>GCC results not available</p>" >> "$unified_report"
    fi
    
    cat >> "$unified_report" << 'EOF'
        </div>
        <p><a href="../gcc/reports/gtest_html/index.html">View GCC HTML Report</a></p>
    </div>
    
    <div class="section clang-section">
        <h2>Clang/LLVM Results</h2>
        <div id="clang-results">
EOF
    
    # Add Clang results if available
    if [ -f "$ORCHESTRATOR_WORKSPACE/clang/reports/combined/summary.txt" ]; then
        echo "            <pre>" >> "$unified_report"
        head -20 "$ORCHESTRATOR_WORKSPACE/clang/reports/combined/summary.txt" >> "$unified_report"
        echo "            </pre>" >> "$unified_report"
    else
        echo "            <p>Clang results not available</p>" >> "$unified_report"
    fi
    
    cat >> "$unified_report" << 'EOF'
        </div>
        <p><a href="../clang/reports/combined/html/index.html">View Clang HTML Report</a></p>
    </div>
    
    <div class="section comparison-section">
        <h2>Comparison Analysis</h2>
        <div id="comparison-results">
EOF
    
    # Add comparison results
    if [ -f "$ORCHESTRATOR_WORKSPACE/comparison/coverage_comparison.txt" ]; then
        echo "            <pre>" >> "$unified_report"
        cat "$ORCHESTRATOR_WORKSPACE/comparison/coverage_comparison.txt" >> "$unified_report"
        echo "            </pre>" >> "$unified_report"
    else
        echo "            <p>Comparison analysis not available</p>" >> "$unified_report"
    fi
    
    cat >> "$unified_report" << 'EOF'
        </div>
    </div>
    
    <div class="section">
        <h2>Quick Actions</h2>
        <ul>
            <li><a href="../gcc/reports/">Browse GCC Reports</a></li>
            <li><a href="../clang/reports/">Browse Clang Reports</a></li>
            <li><a href="../comparison/">View Detailed Comparison</a></li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log "Unified report generated: $unified_report"
}

# Upload results to coverage processing service
upload_to_processing_service() {
    local commit_sha="${GIT_COMMIT:-$(git rev-parse HEAD 2>/dev/null || echo 'unknown')}"
    
    log "Uploading coverage results to processing service"
    
    # Package results for upload
    local upload_package="$ORCHESTRATOR_WORKSPACE/coverage_upload_${commit_sha}.tar.gz"
    
    tar -czf "$upload_package" \
        -C "$ORCHESTRATOR_WORKSPACE" \
        gcc/reports clang/reports comparison reports
    
    # Upload to processing service (customize endpoint)
    local upload_endpoint="${COVERAGE_UPLOAD_ENDPOINT:-http://coverage-service/api/v1/coverage/upload}"
    
    if command -v curl >/dev/null 2>&1; then
        curl -X POST \
            -F "component_name=itasca" \
            -F "commit_sha=$commit_sha" \
            -F "coverage_data=@$upload_package" \
            -F "metadata={\"tools\":[\"gcc\",\"clang\"],\"timestamp\":\"$(date -Iseconds)\"}" \
            "$upload_endpoint" || log "Upload failed, continuing..."
    else
        log "curl not available, skipping upload"
    fi
    
    log "Upload package available at: $upload_package"
}

# Main orchestrator function
main() {
    local workflow_type="${1:-combined}"  # gtest, devtest, or combined
    local tools="${2:-both}"              # gcc, clang, or both
    
    log "Starting unified coverage orchestrator"
    log "Workflow: $workflow_type, Tools: $tools"
    
    setup_orchestrator
    
    # Run workflows based on tool selection
    case "$tools" in
        "gcc")
            run_gcc_workflow "$workflow_type"
            ;;
        "clang")
            run_clang_workflow "$workflow_type"
            ;;
        "both")
            run_gcc_workflow "$workflow_type"
            run_clang_workflow "$workflow_type"
            compare_coverage_results
            ;;
        *)
            error_exit "Unknown tools option: $tools. Use: gcc, clang, or both"
            ;;
    esac
    
    # Generate unified reporting
    generate_unified_report
    
    # Upload results
    upload_to_processing_service
    
    log "Unified coverage orchestrator completed successfully"
    log "Results available in: $ORCHESTRATOR_WORKSPACE"
    
    # Show final summary
    if [ -f "$ORCHESTRATOR_WORKSPACE/reports/unified_coverage_report.html" ]; then
        log "📊 Unified report: file://$ORCHESTRATOR_WORKSPACE/reports/unified_coverage_report.html"
    fi
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
