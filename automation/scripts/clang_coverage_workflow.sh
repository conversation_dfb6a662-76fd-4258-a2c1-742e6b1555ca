#!/bin/bash

# Clang/LLVM Coverage Workflow Automation
# Modern alternative to GCC/gcov with better performance and reporting

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="${WORKSPACE_DIR:-/tmp/clang_coverage_$(date +%Y%m%d_%H%M%S)}"
LOG_FILE="${WORKSPACE_DIR}/clang_coverage.log"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Setup workspace
setup_workspace() {
    log "Setting up Clang coverage workspace: $WORKSPACE_DIR"
    mkdir -p "$WORKSPACE_DIR"/{build,deploy,reports,profraw,merged}
    
    # Set environment variables for Clang coverage
    export LLVM_PROFILE_FILE="$WORKSPACE_DIR/profraw/%p-%m.profraw"
    export LLVM_COV_SHOW_INSTANTIATIONS=1
    
    log "Workspace setup complete"
}

# Build with Clang coverage instrumentation
build_instrumented_binary() {
    local build_dir="$WORKSPACE_DIR/build"
    
    log "Building instrumented binary with Clang coverage"
    
    cd "$build_dir"
    
    # Configure with Clang coverage preset
    cmake --preset x64-linux-cov-clang -S "${SOURCE_DIR:-../../../}"
    
    # Build instrumented binaries
    cmake --build out/x64-linux-cov-clang
    
    # Verify coverage instrumentation
    local binary_path="out/x64-linux-cov-clang/src/zpn_brokerd/zpn_brokerd"
    if [ -f "$binary_path" ]; then
        # Check for LLVM profile symbols
        if objdump -t "$binary_path" | grep -q "__llvm_profile"; then
            log "✅ Binary successfully instrumented with LLVM coverage"
        else
            error_exit "Binary missing LLVM coverage instrumentation"
        fi
    else
        error_exit "Instrumented binary not found: $binary_path"
    fi
    
    # Save build artifacts
    tar -czf "$WORKSPACE_DIR/clang_build_artifacts.tar.gz" out/x64-linux-cov-clang/
    
    log "Build completed successfully"
}

# Run unit tests with Clang coverage
run_gtest_coverage() {
    local build_dir="$WORKSPACE_DIR/build/out/x64-linux-cov-clang"
    
    log "Running unit tests with Clang coverage collection"
    
    cd "$build_dir"
    
    # Clear any existing profile data
    rm -f "$WORKSPACE_DIR/profraw"/*.profraw
    
    # Run unit tests
    ctest --output-on-failure
    
    # Verify profile data was generated
    local profraw_count=$(find "$WORKSPACE_DIR/profraw" -name "*.profraw" | wc -l)
    if [ "$profraw_count" -eq 0 ]; then
        error_exit "No profile data generated during unit tests"
    fi
    
    log "Unit tests completed, generated $profraw_count profile files"
    
    # Generate unit test coverage report
    generate_clang_coverage_report "gtest" "$WORKSPACE_DIR/profraw" "$build_dir"
}

# Deploy and run devtest with Clang coverage
run_devtest_coverage() {
    local target="${DEVTEST_TARGET:-localhost}"
    local build_dir="$WORKSPACE_DIR/build/out/x64-linux-cov-clang"
    
    log "Running devtest coverage on target: $target"
    
    # Deploy instrumented binary
    deploy_clang_binary "$target" "$build_dir/src/zpn_brokerd/zpn_brokerd"
    
    # Configure coverage environment on target
    configure_clang_coverage_environment "$target"
    
    # Run devtest suite
    execute_devtest_suite "$target"
    
    # Collect profile data
    collect_clang_profile_data "$target"
    
    # Generate devtest coverage report
    generate_clang_coverage_report "devtest" "$WORKSPACE_DIR/profraw-devtest" "$build_dir"
}

# Deploy Clang instrumented binary
deploy_clang_binary() {
    local target="$1"
    local binary_path="$2"
    
    log "Deploying Clang instrumented binary to $target"
    
    # Create deployment package
    local deploy_package="$WORKSPACE_DIR/deploy/clang_binary_package.tar.gz"
    tar -czf "$deploy_package" -C "$(dirname "$binary_path")" "$(basename "$binary_path")"
    
    # Copy to target
    scp "$deploy_package" "$target:~/clang_binary_package.tar.gz"
    
    # Deploy using modified script for Clang
    ssh "$target" "
        tar -xzf clang_binary_package.tar.gz
        sudo cp zpn_brokerd /opt/zscaler/bin/zpn_brokerd
        sudo /sbin/restorecon -v /opt/zscaler/bin/zpn_brokerd
        sudo chown zscaler:zscaler /opt/zscaler/bin/zpn_brokerd
        sudo setcap CAP_NET_BIND_SERVICE,CAP_SYS_PTRACE,cap_net_raw,cap_net_admin=ep /opt/zscaler/bin/zpn_brokerd
    "
    
    log "Binary deployment completed"
}

# Configure Clang coverage environment
configure_clang_coverage_environment() {
    local target="$1"
    
    log "Configuring Clang coverage environment on $target"
    
    ssh "$target" "
        # Create profile data directory
        sudo mkdir -p /tmp/clang-coverage
        sudo chown zscaler:zscaler /tmp/clang-coverage
        
        # Configure service environment for LLVM profiling
        sudo tee /opt/zscaler/etc/zpn_brokerd.conf > /dev/null << 'EOF'
LLVM_PROFILE_FILE=/tmp/clang-coverage/%p-%m.profraw
LLVM_COV_SHOW_INSTANTIATIONS=1
EOF
    "
    
    log "Environment configuration completed"
}

# Execute devtest suite
execute_devtest_suite() {
    local target="$1"
    
    log "Executing devtest suite on $target"
    
    ssh "$target" "
        # Restart service with coverage
        sudo systemctl restart zpn_brokerd
        
        # Wait for service to be ready
        sleep 5
        
        # Run devtest suite (customize based on your test framework)
        if [ -f './run_devtest_suite.sh' ]; then
            ./run_devtest_suite.sh
        else
            # Placeholder for actual devtest execution
            echo 'Running devtest workload...'
            sleep 30  # Simulate test execution
        fi
        
        # Graceful shutdown to flush profile data
        itascurl /system/ungraceful_shutdown || sudo systemctl stop zpn_brokerd
        
        # Wait for profile data to be written
        sleep 5
    "
    
    log "DevTest suite execution completed"
}

# Collect Clang profile data
collect_clang_profile_data() {
    local target="$1"
    local dest_dir="$WORKSPACE_DIR/profraw-devtest"
    
    log "Collecting Clang profile data from $target"
    
    mkdir -p "$dest_dir"
    
    # Collect profile data
    ssh "$target" "tar -czf devtest_profraw.tar.gz /tmp/clang-coverage/*.profraw"
    scp "$target:~/devtest_profraw.tar.gz" "$dest_dir/"
    
    # Extract profile data
    cd "$dest_dir"
    tar -xzf devtest_profraw.tar.gz --strip-components=2
    
    local profraw_count=$(find "$dest_dir" -name "*.profraw" | wc -l)
    log "Collected $profraw_count profile files from devtest"
    
    if [ "$profraw_count" -eq 0 ]; then
        error_exit "No profile data collected from devtest"
    fi
}

# Generate Clang coverage report
generate_clang_coverage_report() {
    local report_type="$1"  # gtest, devtest, or combined
    local profraw_dir="$2"
    local build_dir="$3"
    local output_dir="$WORKSPACE_DIR/reports/$report_type"
    
    log "Generating $report_type coverage report using LLVM tools"
    
    mkdir -p "$output_dir"
    
    # Find all profraw files
    local profraw_files=($(find "$profraw_dir" -name "*.profraw"))
    if [ ${#profraw_files[@]} -eq 0 ]; then
        error_exit "No profile data found in $profraw_dir"
    fi
    
    log "Processing ${#profraw_files[@]} profile files"
    
    # Merge profile data
    local merged_profdata="$output_dir/merged.profdata"
    llvm-profdata merge -sparse "${profraw_files[@]}" -o "$merged_profdata"
    
    if [ ! -f "$merged_profdata" ]; then
        error_exit "Failed to merge profile data"
    fi
    
    # Find instrumented binaries
    local binaries=($(find "$build_dir" -type f -executable -exec sh -c 'objdump -t "$1" | grep -q "__llvm_profile"' _ {} \; -print))
    
    if [ ${#binaries[@]} -eq 0 ]; then
        error_exit "No instrumented binaries found in $build_dir"
    fi
    
    log "Found ${#binaries[@]} instrumented binaries"
    
    # Build binary arguments for llvm-cov
    local binary_args=()
    for binary in "${binaries[@]}"; do
        binary_args+=("-object" "$binary")
    done
    
    # Generate different report formats
    generate_llvm_cov_reports "$merged_profdata" "$output_dir" "${binary_args[@]}"
    
    log "$report_type coverage report generated in $output_dir"
}

# Generate LLVM coverage reports in multiple formats
generate_llvm_cov_reports() {
    local profdata_file="$1"
    local output_dir="$2"
    shift 2
    local binary_args=("$@")
    
    local source_dir="${SOURCE_DIR:-../../../src}"
    
    # JSON format (for programmatic consumption)
    log "Generating JSON coverage report"
    llvm-cov export \
        -format=text \
        -instr-profile="$profdata_file" \
        "${binary_args[@]}" \
        > "$output_dir/coverage.json"
    
    # LCOV format (for compatibility with existing tools)
    log "Generating LCOV coverage report"
    llvm-cov export \
        -format=lcov \
        -instr-profile="$profdata_file" \
        "${binary_args[@]}" \
        > "$output_dir/coverage.lcov"
    
    # HTML format (for human consumption)
    log "Generating HTML coverage report"
    llvm-cov show \
        -format=html \
        -instr-profile="$profdata_file" \
        -output-dir="$output_dir/html" \
        "${binary_args[@]}"
    
    # Text summary
    log "Generating text summary"
    llvm-cov report \
        -instr-profile="$profdata_file" \
        "${binary_args[@]}" \
        > "$output_dir/summary.txt"
    
    # Show summary in log
    log "Coverage Summary:"
    llvm-cov report \
        -instr-profile="$profdata_file" \
        "${binary_args[@]}" | head -20 | while read line; do
        log "  $line"
    done
}

# Combine multiple coverage reports
combine_coverage_reports() {
    log "Combining GTest and DevTest coverage reports"
    
    local gtest_profraw="$WORKSPACE_DIR/profraw"
    local devtest_profraw="$WORKSPACE_DIR/profraw-devtest"
    local combined_profraw="$WORKSPACE_DIR/profraw-combined"
    local build_dir="$WORKSPACE_DIR/build/out/x64-linux-cov-clang"
    
    mkdir -p "$combined_profraw"
    
    # Copy all profile data to combined directory
    if [ -d "$gtest_profraw" ]; then
        cp "$gtest_profraw"/*.profraw "$combined_profraw/" 2>/dev/null || true
    fi
    
    if [ -d "$devtest_profraw" ]; then
        cp "$devtest_profraw"/*.profraw "$combined_profraw/" 2>/dev/null || true
    fi
    
    # Generate combined report
    generate_clang_coverage_report "combined" "$combined_profraw" "$build_dir"
    
    # Generate comparison analysis
    generate_clang_coverage_analysis
}

# Generate coverage analysis and comparison
generate_clang_coverage_analysis() {
    local reports_dir="$WORKSPACE_DIR/reports"
    local analysis_file="$reports_dir/clang_coverage_analysis.txt"
    
    log "Generating Clang coverage analysis"
    
    {
        echo "============================================================"
        echo "CLANG/LLVM CODE COVERAGE ANALYSIS REPORT"
        echo "============================================================"
        echo "Generated: $(date)"
        echo "Workspace: $WORKSPACE_DIR"
        echo ""
        
        for report_type in gtest devtest combined; do
            local summary_file="$reports_dir/$report_type/summary.txt"
            if [ -f "$summary_file" ]; then
                echo "=== ${report_type^^} COVERAGE ==="
                head -10 "$summary_file"
                echo ""
            fi
        done
        
        echo "============================================================"
    } > "$analysis_file"
    
    log "Analysis report generated: $analysis_file"
}

# Main workflow function
main() {
    local workflow_type="${1:-combined}"  # gtest, devtest, or combined
    
    log "Starting Clang coverage workflow: $workflow_type"
    
    setup_workspace
    build_instrumented_binary
    
    case "$workflow_type" in
        "gtest")
            run_gtest_coverage
            ;;
        "devtest")
            run_devtest_coverage
            ;;
        "combined")
            run_gtest_coverage
            run_devtest_coverage
            combine_coverage_reports
            ;;
        *)
            error_exit "Unknown workflow type: $workflow_type. Use: gtest, devtest, or combined"
            ;;
    esac
    
    log "Clang coverage workflow completed successfully"
    log "Reports available in: $WORKSPACE_DIR/reports"
    
    # Generate final summary
    if [ -f "$WORKSPACE_DIR/reports/combined/summary.txt" ]; then
        log "Final Coverage Summary:"
        head -10 "$WORKSPACE_DIR/reports/combined/summary.txt" | while read line; do
            log "  $line"
        done
    fi
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
