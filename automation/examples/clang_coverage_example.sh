#!/bin/bash

# Clang Coverage Example - Following the Complete Workflow
# This demonstrates the exact Clang coverage workflow you provided

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}==== $1 ====${NC}"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking Prerequisites"
    
    local missing_tools=()
    
    for tool in clang clang++ llvm-profdata llvm-cov cmake; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo "Missing required tools: ${missing_tools[*]}"
        echo "Please install LLVM/Clang toolchain:"
        echo "  Ubuntu/Debian: sudo apt install clang llvm"
        echo "  RHEL/CentOS: sudo yum install clang llvm"
        echo "  macOS: brew install llvm"
        exit 1
    fi
    
    print_info "All prerequisites available ✅"
}

# Step 1: Compile with Coverage Enabled
compile_with_coverage() {
    print_step "Step 1: Compile with Coverage Enabled"
    
    local workspace="/tmp/clang_coverage_example_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$workspace"
    cd "$workspace"
    
    print_info "Workspace: $workspace"
    
    # Configure CMake with Clang coverage flags
    print_info "Configuring CMake with Clang coverage instrumentation..."
    
    cmake -S "${SOURCE_DIR:-$(pwd)}" \
          -B build \
          -DCMAKE_C_COMPILER=clang \
          -DCMAKE_CXX_COMPILER=clang++ \
          -DCMAKE_C_FLAGS="-g -fPIC -fprofile-instr-generate -fcoverage-mapping" \
          -DCMAKE_CXX_FLAGS="-g -fPIC -fprofile-instr-generate -fcoverage-mapping" \
          -DCMAKE_BUILD_TYPE=Debug \
          -DVCPKG_TARGET_TRIPLET=x64-linux-debug
    
    print_info "Building with coverage instrumentation..."
    cmake --build build --parallel
    
    # Verify instrumentation
    print_info "Verifying coverage instrumentation..."
    local instrumented_count=0
    
    find build -type f -executable | while read binary; do
        if objdump -t "$binary" 2>/dev/null | grep -q "__llvm_profile"; then
            echo "  ✅ $binary (instrumented)"
            ((instrumented_count++))
        fi
    done
    
    if [ $instrumented_count -eq 0 ]; then
        print_warning "No instrumented binaries found"
    else
        print_info "Found $instrumented_count instrumented binaries"
    fi
    
    export WORKSPACE="$workspace"
}

# Step 2: Run the Instrumented Program
run_instrumented_program() {
    print_step "Step 2: Run the Instrumented Program"
    
    cd "$WORKSPACE/build"
    
    # Set profile file pattern
    export LLVM_PROFILE_FILE="$WORKSPACE/coverage_data_%p_%m.profraw"
    
    print_info "Running unit tests with LLVM profiling..."
    print_info "Profile pattern: $LLVM_PROFILE_FILE"
    
    # Run tests
    ctest --output-on-failure || print_warning "Some tests failed, continuing with coverage..."
    
    # Check generated profile files
    local profraw_files=($(find "$WORKSPACE" -name "*.profraw" 2>/dev/null))
    
    if [ ${#profraw_files[@]} -eq 0 ]; then
        print_warning "No profile data generated"
        print_info "This might happen if:"
        print_info "  - Tests didn't run successfully"
        print_info "  - Binaries weren't properly instrumented"
        print_info "  - Profile directory isn't writable"
        return 1
    fi
    
    print_info "Generated ${#profraw_files[@]} profile files:"
    for file in "${profraw_files[@]}"; do
        local size=$(du -h "$file" | cut -f1)
        echo "  📄 $(basename "$file") ($size)"
    done
}

# Step 3a: Index the Raw Profile
index_raw_profile() {
    print_step "Step 3a: Index the Raw Profile"
    
    cd "$WORKSPACE"
    
    # Find all profraw files
    local profraw_files=($(find . -name "*.profraw"))
    
    if [ ${#profraw_files[@]} -eq 0 ]; then
        print_warning "No profile files to index"
        return 1
    fi
    
    print_info "Merging ${#profraw_files[@]} profile files..."
    
    # Merge with sparse flag for smaller indexed profiles
    llvm-profdata merge -sparse "${profraw_files[@]}" -o merged_coverage.profdata
    
    if [ -f "merged_coverage.profdata" ]; then
        local size=$(du -h merged_coverage.profdata | cut -f1)
        print_info "Created indexed profile: merged_coverage.profdata ($size)"
    else
        print_warning "Failed to create indexed profile"
        return 1
    fi
}

# Step 3b: Generate Coverage Reports
generate_coverage_reports() {
    print_step "Step 3b: Generate Coverage Reports"
    
    cd "$WORKSPACE"
    
    if [ ! -f "merged_coverage.profdata" ]; then
        print_warning "No indexed profile found"
        return 1
    fi
    
    # Find instrumented binaries
    local binaries=($(find build -type f -executable -exec sh -c 'objdump -t "$1" 2>/dev/null | grep -q "__llvm_profile"' _ {} \; -print))
    
    if [ ${#binaries[@]} -eq 0 ]; then
        print_warning "No instrumented binaries found for reporting"
        return 1
    fi
    
    print_info "Found ${#binaries[@]} instrumented binaries for reporting"
    
    # Build binary arguments
    local binary_args=()
    for binary in "${binaries[@]}"; do
        binary_args+=("-object" "$binary")
    done
    
    # Generate different report types
    mkdir -p reports
    
    # 1. Line-oriented report
    print_info "Generating line-oriented report..."
    llvm-cov show "${binary_args[@]}" -instr-profile=merged_coverage.profdata > reports/line_report.txt
    
    # 2. Coverage summary
    print_info "Generating coverage summary..."
    llvm-cov report "${binary_args[@]}" -instr-profile=merged_coverage.profdata > reports/summary.txt
    
    # 3. HTML report
    print_info "Generating HTML report..."
    llvm-cov show "${binary_args[@]}" -instr-profile=merged_coverage.profdata -format=html -o reports/html
    
    # 4. Detailed report with advanced options
    print_info "Generating detailed report with advanced options..."
    llvm-cov show "${binary_args[@]}" -instr-profile=merged_coverage.profdata \
        --show-line-counts-or-regions \
        --show-branches=count \
        --show-expansions > reports/detailed_report.txt
    
    # 5. Export to JSON (for programmatic consumption)
    print_info "Exporting to JSON format..."
    llvm-cov export "${binary_args[@]}" -instr-profile=merged_coverage.profdata > reports/coverage.json
    
    # 6. Export to LCOV format (for compatibility)
    print_info "Exporting to LCOV format..."
    llvm-cov export "${binary_args[@]}" -instr-profile=merged_coverage.profdata -format=lcov > reports/coverage.lcov
    
    print_info "All reports generated successfully ✅"
}

# Show coverage statistics
show_coverage_statistics() {
    print_step "Coverage Statistics"
    
    cd "$WORKSPACE"
    
    if [ -f "reports/summary.txt" ]; then
        echo ""
        echo "📊 Coverage Summary:"
        head -20 reports/summary.txt
        echo ""
        
        # Extract key metrics
        if grep -q "TOTAL" reports/summary.txt; then
            echo "🎯 Key Metrics:"
            grep "TOTAL" reports/summary.txt | while read line; do
                echo "  $line"
            done
        fi
    else
        print_warning "Summary report not available"
    fi
    
    echo ""
    echo "📁 Generated Reports:"
    if [ -d "reports" ]; then
        find reports -type f | while read file; do
            local size=$(du -h "$file" | cut -f1)
            echo "  📄 $file ($size)"
        done
        
        if [ -f "reports/html/index.html" ]; then
            echo ""
            echo "🌐 HTML Report: file://$WORKSPACE/reports/html/index.html"
        fi
    fi
}

# Cleanup function
cleanup() {
    print_step "Cleanup"
    
    if [ -n "${WORKSPACE:-}" ] && [ -d "$WORKSPACE" ]; then
        echo "Workspace preserved at: $WORKSPACE"
        echo "To clean up: rm -rf $WORKSPACE"
    fi
}

# Main function
main() {
    echo "Clang Coverage Example - Complete Workflow"
    echo "=========================================="
    echo ""
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Run the complete workflow
    check_prerequisites
    compile_with_coverage
    
    if run_instrumented_program; then
        if index_raw_profile; then
            generate_coverage_reports
            show_coverage_statistics
        fi
    else
        print_warning "Skipping report generation due to missing profile data"
    fi
    
    echo ""
    echo "✅ Clang coverage workflow completed!"
    echo ""
    echo "This example demonstrates:"
    echo "  1. Compiling with -fprofile-instr-generate -fcoverage-mapping"
    echo "  2. Running instrumented programs with LLVM_PROFILE_FILE"
    echo "  3. Indexing raw profiles with llvm-profdata merge -sparse"
    echo "  4. Generating reports with llvm-cov show/report/export"
    echo "  5. Advanced options for detailed analysis"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
